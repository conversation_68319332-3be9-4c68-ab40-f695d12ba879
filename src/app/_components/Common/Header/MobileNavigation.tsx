'use client';

import { ServiceNavigationMenuMobile } from '@/src/app/_components';
import { CancellationInfo } from '@/src/app/_components/Pages/Home/CancellationInfo';

interface MobileNavigationProps {
  isMenuOpen: boolean;
  toggleMenu: () => void;
}

export default function MobileNavigation({ isMenuOpen, toggleMenu }: MobileNavigationProps) {
  if (!isMenuOpen) return null;

  return (
    <nav className="fixed inset-0 z-50 md:hidden" style={{ top: '130px' }}>
      <div className="h-full overflow-y-auto bg-white">
        {/* Menu content */}
        <ServiceNavigationMenuMobile onLinkClick={toggleMenu} />

        {/* Cancellation Info */}
        <div className="border-t border-gray-200">
          <CancellationInfo />
        </div>
      </div>
    </nav>
  );
}
