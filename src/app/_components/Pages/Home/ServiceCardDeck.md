# ServiceCardDeck Component

A dynamic card deck visualization component that displays service images in a layered, overlapping pattern based on the Figma design.

## Features

- **Dynamic Data**: Fetches service images from API data
- **Layered Design**: Multiple cards with different sizes and positions
- **Gradient Backgrounds**: Decorative gradient cards behind service images
- **Responsive**: Scales appropriately for different screen sizes
- **Performance Optimized**: Uses `useMemo` to prevent unnecessary re-renders

## Usage

```tsx
import { ServiceCardDeck } from '@/src/app/_components/Pages/Home/ServiceCardDeck';

// Basic usage
<ServiceCardDeck services={services} />

// With custom styling
<ServiceCardDeck 
  services={services} 
  className="custom-class" 
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `services` | `Service[]` | `[]` | Array of service objects with image URLs |
| `className` | `string` | `''` | Additional CSS classes to apply |

## Service Data Structure

The component expects services with the following structure:

```typescript
interface Service {
  id: number;
  name: string;
  imageUrl: string;
  // ... other service properties
}
```

## Design Implementation

The component implements the Figma design with:

- **Background Cards**: Gradient and solid colored cards positioned behind service images
- **Service Image Cards**: Up to 3 service images displayed in layered arrangement
- **Shadows and Effects**: Box shadows and border radius matching the design
- **Responsive Scaling**: Uses CSS transforms for mobile responsiveness

## Performance Notes

- Filters services to only include those with valid image URLs
- Limits to maximum 6 services for performance
- Uses `useMemo` to prevent recalculation on every render
- Returns `null` when no valid services are available

## Integration

The component is integrated into the Hero section of the home page:

```tsx
// In Hero.tsx
<ServiceCardDeck services={allServices} className="flex-shrink-0 scale-75 md:scale-90 lg:scale-100" />
```

## Testing

Comprehensive test suite covers:
- Rendering with and without services
- Filtering services without images
- Custom className application
- Service limit enforcement

Run tests with:
```bash
npm test -- ServiceCardDeck.test.tsx
```
