# ServiceCardDeck Component

A realistic card stack visualization component that displays exactly 6 cards: 3 service image cards on top and 3 decorative gradient cards underneath, simulating physical cards casually stacked on top of each other.

## Features

- **6-Card Stack**: Always displays exactly 6 cards for consistent visual impact
- **Service Image Cards**: Top 3 cards display actual service images from API data
- **Decorative Cards**: Bottom 3 cards show gradient backgrounds based on Figma designs
- **Realistic Stacking**: Simulates physical cards with staggered positioning and natural rotation
- **Dynamic Depth**: Progressive shadow intensity and scale variations create 3D depth illusion
- **Rotation Effects**: Subtle rotation angles (-4° to +3°) for organic, non-aligned appearance
- **Interactive Hover**: Service cards lift and straighten on hover with smooth transitions
- **Progressive Shadows**: Cards deeper in the stack have more pronounced shadows
- **Scale Variations**: Cards scale from 0.78 to 1.0 based on stack position
- **Figma-Accurate Colors**: Decorative cards use exact gradients and colors from Figma designs
- **Performance Optimized**: Uses `useMemo` to prevent unnecessary re-renders

## Usage

```tsx
import { ServiceCardDeck } from '@/src/app/_components/Pages/Home/ServiceCardDeck';

// Basic usage
<ServiceCardDeck services={services} />

// With custom styling
<ServiceCardDeck
  services={services}
  className="custom-class"
/>
```

## Props

| Prop        | Type                | Default | Description                          |
| ----------- | ------------------- | ------- | ------------------------------------ |
| `services`  | `ServiceCategory[]` | `[]`    | Array of service categories from API |
| `className` | `string`            | `''`    | Additional CSS classes to apply      |

## Card Stack Structure

The component renders exactly 6 cards in two layers:

### Top Layer (Service Cards - Indices 0-2)

- **Card 1**: Front-most service image (z-index: 5)
- **Card 2**: Second service image (z-index: 4)
- **Card 3**: Third service image (z-index: 3)

### Bottom Layer (Decorative Cards - Indices 3-5)

- **Card 4**: Golden gradient card (z-index: 0)
- **Card 5**: Golden gradient card (z-index: 1)
- **Card 6**: Black decorative card (z-index: 2)

## Figma Design Implementation

The decorative cards use exact colors from Figma designs:

- **Golden Gradient Cards**: `linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)`
- **Black Card**: `#000000`
- **Border Radius**: Matches Figma specifications (24px or 16px)

## Card Configuration

Each card has unique positioning and styling:

```typescript
interface CardConfig {
  x: number; // Horizontal offset (0-65px)
  y: number; // Vertical offset (0-50px)
  rotation: number; // Rotation angle (-4° to +3°)
  scale: number; // Scale factor (0.78 to 1.0)
  zIndex: number; // Stacking order (0-5)
  shadowIntensity: number; // Shadow depth (0.15-0.4)
  width: number; // Card width (190-225px)
  height: number; // Card height (270-305px)
  type: 'service' | 'decorative';
  background?: string; // Background for decorative cards
  borderRadius?: string; // Custom border radius
}
```

## Behavior

- **With Services**: Displays up to 3 service images + 3 decorative cards
- **Without Services**: Shows only 3 decorative cards (service cards are hidden)
- **Partial Services**: If fewer than 3 services available, shows available services + decorative cards
- **Hover Effects**: Only service cards have interactive hover effects
- **Responsive**: Scales appropriately for different screen sizes

## Performance Notes

- Filters services to only include those with valid image URLs
- Limits to maximum 3 service images for optimal performance
- Uses `useMemo` to prevent recalculation on every render
- Priority loading for first 3 images
- Optimized shadow calculations for smooth animations

## Integration

The component is integrated into the Hero section:

```tsx
// In Hero.tsx
<ServiceCardDeck
  services={allServices}
  className="flex-shrink-0 scale-75 md:scale-90 lg:scale-100"
/>
```

## Testing

Comprehensive test suite covers:

- Rendering decorative cards without services
- Rendering service images when available
- Custom className application
- Service filtering functionality

Run tests with:

```bash
npm test -- ServiceCardDeck.test.tsx
```
