'use client';

import { Service } from '@/src/app/_interfaces/service-type';
import Image from 'next/image';
import { useMemo } from 'react';

interface ServiceCardDeckProps {
  services?: Service[];
  className?: string;
}

export const ServiceCardDeck = ({ services = [], className = '' }: ServiceCardDeckProps) => {
  // Use useMemo to avoid recalculating on every render
  const displayServices = useMemo(() => {
    return services
      .filter((service) => service.imageUrl && service.imageUrl.trim() !== '')
      .slice(0, 6);
  }, [services]);

  if (displayServices.length === 0) {
    return null;
  }

  // Card stack configuration with staggered positioning, rotation, and depth
  const cardStackConfig = [
    // Bottom card (furthest back)
    {
      x: 90,
      y: 49,
      rotation: 12.588,
      scale: 0.85,
      zIndex: 1,
      shadowIntensity: 0.3,
      width: 240,
      height: 320,
    },
    // Middle card
    {
      x: 80,
      y: 20,
      rotation: 1.454,
      scale: 0.92,
      zIndex: 2,
      shadowIntensity: 0.25,
      width: 210,
      height: 290,
    },
    // Top card (closest to viewer)
    {
      x: 0,
      y: 0,
      rotation: -6.378,
      scale: 1,
      zIndex: 6,
      shadowIntensity: 0.15,
      width: 240,
      height: 320,
    },
    // Additional cards for more depth
    {
      x: 65,
      y: 50,
      rotation: 3,
      scale: 0.78,
      zIndex: 0,
      shadowIntensity: 0.4,
      width: 190,
      height: 270,
    },
    {
      x: 15,
      y: 10,
      rotation: -2,
      scale: 0.96,
      zIndex: 4,
      shadowIntensity: 0.1,
      width: 225,
      height: 305,
    },
    {
      x: 35,
      y: 25,
      rotation: 1,
      scale: 0.88,
      zIndex: 1,
      shadowIntensity: 0.35,
      width: 195,
      height: 275,
    },
  ];

  return (
    <div className={`relative ${className}`}>
      <div className="relative h-[400px] w-[300px] max-w-full">
        {/* Realistic card stack with staggered positioning and depth */}
        {displayServices.map((service, index) => {
          const config = cardStackConfig[index];
          if (!config) return null;

          return (
            <div
              key={service.id}
              className="group absolute cursor-pointer overflow-hidden rounded-2xl transition-all duration-500 ease-out hover:z-50 hover:rotate-0 hover:scale-110 hover:shadow-2xl"
              style={{
                left: `${config.x}px`,
                top: `${config.y}px`,
                width: `${config.width}px`,
                height: `${config.height}px`,
                transform: `rotate(${config.rotation}deg) scale(${config.scale})`,
                zIndex: config.zIndex,
                boxShadow: `
                  0px ${8 + config.shadowIntensity * 20}px ${16 + config.shadowIntensity * 30}px -${4 + config.shadowIntensity * 8}px rgba(0, 0, 0, ${0.1 + config.shadowIntensity * 0.3}),
                  0px ${4 + config.shadowIntensity * 12}px ${8 + config.shadowIntensity * 20}px -${2 + config.shadowIntensity * 6}px rgba(0, 0, 0, ${0.05 + config.shadowIntensity * 0.2}),
                  0px ${2 + config.shadowIntensity * 8}px ${4 + config.shadowIntensity * 12}px -${1 + config.shadowIntensity * 4}px rgba(0, 0, 0, ${0.03 + config.shadowIntensity * 0.15})
                `,
                transformOrigin: 'center center',
              }}
            >
              {/* Card border with gradient */}
              <div
                className="absolute inset-0 rounded-2xl"
                style={{
                  background: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)',
                  padding: '3px',
                }}
              >
                {/* Inner card content */}
                <div className="relative h-full w-full overflow-hidden rounded-2xl bg-white">
                  {/* Service image */}
                  <div className="relative h-full w-full">
                    <Image
                      src={service.imageUrl}
                      alt={service.name}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                      sizes={`${config.width}px`}
                      quality={85}
                      priority={index < 3}
                    />
                    {/* Subtle overlay for depth */}
                    <div
                      className="absolute inset-0 bg-black transition-opacity duration-300 group-hover:opacity-0"
                      style={{
                        opacity: config.shadowIntensity * 0.2,
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
