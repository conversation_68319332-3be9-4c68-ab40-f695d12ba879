'use client';

import { Service } from '@/src/app/_interfaces/service-type';
import Image from 'next/image';
import { useMemo } from 'react';

interface ServiceCardDeckProps {
  services?: Service[];
  className?: string;
}

export const ServiceCardDeck = ({ services = [], className = '' }: ServiceCardDeckProps) => {
  // Use useMemo to avoid recalculating on every render
  const displayServices = useMemo(() => {
    return services
      .filter((service) => service.imageUrl && service.imageUrl.trim() !== '')
      .slice(0, 6);
  }, [services]);

  if (displayServices.length === 0) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <div className="relative h-[352px] w-[434px] max-w-full">
        {/* Background gradient cards */}
        <div
          className="absolute rounded-r-2xl shadow-sm"
          style={{
            left: '316px',
            top: '66px',
            width: '108px',
            height: '225px',
            background: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)',
            borderRadius: '0px 24px 24px 0px',
            boxShadow: '0px 2px 4px -1px rgba(0, 0, 0, 0.06), 0px 4px 6px -1px rgba(0, 0, 0, 0.1)',
          }}
        />

        <div
          className="absolute rounded-r-2xl shadow-sm"
          style={{
            left: '298px',
            top: '45px',
            width: '111px',
            height: '252px',
            background: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)',
            borderRadius: '0px 24px 24px 0px',
            boxShadow: '0px 2px 4px -1px rgba(0, 0, 0, 0.06), 0px 4px 6px -1px rgba(0, 0, 0, 0.1)',
          }}
        />

        <div
          className="absolute rounded-r-2xl bg-black shadow-sm"
          style={{
            left: '343px',
            top: '93px',
            width: '91px',
            height: '187px',
            borderRadius: '0px 16px 16px 0px',
            boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.06), 0px 1px 3px 0px rgba(0, 0, 0, 0.1)',
          }}
        />

        {/* Service image cards */}
        {displayServices.length > 0 && (
          <div
            className="absolute overflow-hidden shadow-lg"
            style={{
              left: '224px',
              top: '17px',
              width: '162px',
              height: '291px',
              borderRadius: '0px 24px 24px 0px',
              boxShadow:
                '0px 4px 6px -2px rgba(0, 0, 0, 0.05), 0px 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '4px solid transparent',
              backgroundImage: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)',
              backgroundOrigin: 'border-box',
              backgroundClip: 'border-box',
              rotate: '-6.38deg',
            }}
          >
            <div
              className="relative h-full w-full overflow-hidden"
              style={{
                borderRadius: '0px 20px 20px 0px',
                background: 'rgba(0, 0, 0, 0.3)',
              }}
            >
              <Image
                src={displayServices[0].imageUrl}
                alt={displayServices[0].name}
                fill
                className="object-cover"
                sizes="162px"
                quality={75}
              />
            </div>
          </div>
        )}

        {displayServices.length > 1 && (
          <div
            className="absolute overflow-hidden shadow-xl"
            style={{
              left: '158px',
              top: '8px',
              width: '145px',
              height: '308px',
              borderRadius: '0px 24px 24px 0px',
              boxShadow:
                '14px 10px 10px -5px rgba(0, 0, 0, 0.2), 4px 20px 20px -5px rgba(0, 0, 0, 0.14)',
              border: '4px solid transparent',
              backgroundImage: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)',
              backgroundOrigin: 'border-box',
              backgroundClip: 'border-box',
            }}
          >
            <div
              className="relative h-full w-full overflow-hidden"
              style={{
                borderRadius: '0px 20px 20px 0px',
                background: 'rgba(0, 0, 0, 0.15)',
              }}
            >
              <Image
                src={displayServices[1].imageUrl}
                alt={displayServices[1].name}
                fill
                className="object-cover"
                sizes="145px"
                quality={75}
              />
            </div>
          </div>
        )}

        {displayServices.length > 2 && (
          <div
            className="absolute overflow-hidden shadow-xl"
            style={{
              left: '0px',
              top: '0px',
              width: '248px',
              height: '352px',
              borderRadius: '24px',
              boxShadow:
                '30px 25px 20px -12px rgba(0, 0, 0, 0.15), 10px 4px 54px 0px rgba(0, 0, 0, 0.15)',
            }}
          >
            <Image
              src={displayServices[2].imageUrl}
              alt={displayServices[2].name}
              fill
              className="rounded-3xl object-cover"
              sizes="248px"
              quality={75}
              priority
            />
          </div>
        )}
      </div>
    </div>
  );
};
